{"title": "Human Resource Management (HRM)", "description": "Manage human resources and recruitment", "modules": {"employees": {"title": "Employees", "description": "Manage employee information", "countLabel": "Employees"}, "departments": {"title": "Departments", "description": "Manage organizational structure", "countLabel": "Departments"}, "recruitment": {"title": "Recruitment", "description": "Manage recruitment process", "countLabel": "Positions"}, "attendance": {"title": "Attendance", "description": "Manage attendance and working hours", "countLabel": "Days"}, "leave": {"title": "Leave", "description": "Manage leave and absences", "countLabel": "Requests"}, "payroll": {"title": "Payroll", "description": "Manage salary and benefits", "countLabel": "Payroll period"}, "training": {"title": "Training", "description": "Manage training and development", "countLabel": "Courses"}}, "employee": {"title": "Employee Management", "form": {"title": "Add New Employee", "description": "Enter new employee information", "createTitle": "Add New Employee", "editTitle": "Update Employee Information", "basicInfo": "Basic Information", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "emergencyContact": "Emergency Contact", "employeeCode": "Employee Code", "jobTitle": "Job Title", "jobLevel": "Job Level", "departmentId": "Department", "managerId": "Manager", "employmentType": "Employment Type", "status": "Status", "hireDate": "Hire Date", "probationEndDate": "Probation End Date", "dateOfBirth": "Date of Birth", "gender": "Gender", "maritalStatus": "Marital Status", "numberOfDependents": "Number of Dependents", "address": "Address", "city": "City", "state": "State/Province", "country": "Country", "postalCode": "Postal Code", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "emergencyContactRelationship": "Relationship", "notes": "Notes", "selectDepartment": "Select Department", "selectManager": "Select Manager", "selectEmploymentType": "Select Employment Type", "selectStatus": "Select Status", "selectGender": "Select Gender", "selectMaritalStatus": "Select Marital Status", "createUserTitle": "Create User Account for Employee", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "fullName": "Full Name", "employee": "Employee", "employeePlaceholder": "Search employee..."}, "status": {"active": "Active", "inactive": "Inactive", "on_leave": "On Leave", "terminated": "Terminated", "probation": "Probation", "suspended": "Suspended"}, "employmentType": {"full_time": "Full Time", "part_time": "Part Time", "contract": "Contract", "intern": "Intern", "no_contract": "No Contract"}, "maritalStatus": {"single": "Single", "married": "Married", "divorced": "Divorced", "widowed": "Widowed", "separated": "Separated"}, "gender": {"male": "Male", "female": "Female", "other": "Other"}, "table": {"employeeCode": "Emp ID", "jobTitle": "Job Title", "department": "Department", "status": "Status", "hireDate": "Hire Date", "employeeName": "Employee Name"}, "bulkDeleteConfirmMessage": "Are you sure you want to delete {{count}} selected employees?", "tabs": {"list": "List", "grid": "Grid", "stats": "Statistics"}, "actions": {"roles": "Roles", "permissions": "Permissions"}, "roleModal": {"title": "Role Assignment"}, "permissionModal": {"title": "Direct Permissions"}, "permission": {"title": "Employee Role Assignment", "description": "Select roles to assign to this employee"}, "directPermission": {"title": "Direct Permission Assignment", "description": "Select permissions to directly assign to this employee"}, "stats": {"title": "Employee Statistics", "description": "Statistics feature is under development"}, "statistics": {"title": "Employee Statistics", "description": "Overview of company human resources situation", "totalEmployees": "Total Employees", "totalEmployeesDesc": "All employees", "totalUsers": "Total User Accounts", "totalUsersDesc": "Accounts created", "activeEmployees": "Active Employees", "inactiveEmployees": "Inactive Employees", "newEmployeesThisMonth": "New Employees This Month", "newEmployeesDesc": "Joined this month", "employeesOnProbation": "Employees on Probation", "probationDesc": "Currently on probation period", "departmentDistribution": "Department Distribution", "employmentTypeDistribution": "Employment Type Distribution", "averageServiceYears": "Average Service Years", "upcomingProbationEnds": "Upcoming Probation Ends", "accountCoverage": "Account Coverage Rate", "growthRate": "Monthly Growth Rate"}}, "permission": {"permissionCount": "{{count}} permissions", "noRoles": "No roles available", "noPermissions": "No permissions available", "form": {"permissions": "Permissions"}}, "department": {"title": "Department Management", "form": {"title": "Add New Department", "description": "Description", "name": "Department Name", "parentDepartment": "Parent Department", "manager": "Manager"}, "table": {"name": "Department Name", "description": "Description", "parentDepartment": "Parent Department", "manager": "Manager"}, "filter": {"noParent": "Top Level Departments", "hasParent": "Sub Departments"}, "bulkDeleteConfirmMessage": "Are you sure you want to delete {{count}} selected departments?", "tree": {"title": "Organizational Structure", "noDepartments": "No departments", "totalDepartments": "Total: {{count}} departments"}}}
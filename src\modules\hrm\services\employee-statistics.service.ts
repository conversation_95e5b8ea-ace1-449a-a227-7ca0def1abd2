/**
 * Service cho thống kê nhân viên
 */
import { apiClient } from '@/shared/api/axios';
import { ApiResponse } from '@/shared/types/api.types';

/**
 * Interface cho phân bố phòng ban
 */
export interface DepartmentDistribution {
  departmentName: string;
  employeeCount: number;
}

/**
 * Interface cho phân bố loại hợp đồng
 */
export interface EmploymentTypeDistribution {
  type: string;
  count: number;
}

/**
 * Interface cho thống kê nhân viên overview từ API
 */
export interface EmployeeOverview {
  totalEmployees: number;
  totalUserAccounts: number;
  activeEmployees: number;
  inactiveEmployees: number;
  newEmployeesThisMonth: number;
  probationEmployees: number;
  activeEmployeePercentage: number;
  accountCoveragePercentage: number;
}

/**
 * Interface cho thống kê nhân viên đầy đủ (bao gồm cả overview và các thống kê khác)
 */
export interface EmployeeStatistics extends EmployeeOverview {
  departmentDistribution: DepartmentDistribution[];
  employmentTypeDistribution: EmploymentTypeDistribution[];
  averageServiceYears: number;
  upcomingProbationEnds: number;
}

/**
 * Service cho thống kê nhân viên
 */
export class EmployeeStatisticsService {
  private static readonly BASE_URL = '/api/hrm/employees';

  /**
   * Lấy thống kê tổng quan nhân viên từ API
   */
  static async getEmployeeOverview(): Promise<ApiResponse<EmployeeOverview>> {
    const response = await apiClient.get<ApiResponse<EmployeeOverview>>(`${this.BASE_URL}/overview`);
    return response.data;
  }

  /**
   * Lấy thống kê tổng quan nhân viên (kết hợp overview với mock data cho các thống kê khác)
   */
  static async getEmployeeStatistics(): Promise<ApiResponse<EmployeeStatistics>> {
    try {
      // Lấy dữ liệu overview từ API thực
      const overviewResponse = await this.getEmployeeOverview();
      const overview = overviewResponse.result;

      // Mock data cho các thống kê khác (sẽ được thay thế bằng API thực sau)
      const additionalStats = {
        departmentDistribution: [
          { departmentName: 'Công nghệ thông tin', employeeCount: Math.floor(overview.totalEmployees * 0.4) },
          { departmentName: 'Kinh doanh', employeeCount: Math.floor(overview.totalEmployees * 0.25) },
          { departmentName: 'Marketing', employeeCount: Math.floor(overview.totalEmployees * 0.15) },
          { departmentName: 'Nhân sự', employeeCount: Math.floor(overview.totalEmployees * 0.1) },
          { departmentName: 'Kế toán', employeeCount: Math.floor(overview.totalEmployees * 0.1) },
        ],
        employmentTypeDistribution: [
          { type: 'full_time', count: Math.floor(overview.totalEmployees * 0.8) },
          { type: 'part_time', count: Math.floor(overview.totalEmployees * 0.1) },
          { type: 'contract', count: Math.floor(overview.totalEmployees * 0.08) },
          { type: 'intern', count: Math.floor(overview.totalEmployees * 0.02) },
        ],
        averageServiceYears: 2.5,
        upcomingProbationEnds: Math.floor(overview.probationEmployees * 0.4),
      };

      const combinedData: EmployeeStatistics = {
        ...overview,
        ...additionalStats,
      };

      return {
        success: true,
        result: combinedData,
        message: 'Success',
      };
    } catch (error) {
      console.error('Error fetching employee statistics:', error);
      throw error;
    }
  }

  /**
   * Lấy thống kê nhân viên theo khoảng thời gian
   */
  static async getEmployeeStatisticsByDateRange(
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<EmployeeStatistics>> {
    const response = await apiClient.get<ApiResponse<EmployeeStatistics>>(
      `${this.BASE_URL}/date-range`,
      {
        params: { startDate, endDate },
      }
    );
    return response.data;
  }

  /**
   * Lấy thống kê nhân viên theo phòng ban
   */
  static async getEmployeeStatisticsByDepartment(
    departmentId: number
  ): Promise<ApiResponse<EmployeeStatistics>> {
    const response = await apiClient.get<ApiResponse<EmployeeStatistics>>(
      `${this.BASE_URL}/department/${departmentId}`
    );
    return response.data;
  }
}
